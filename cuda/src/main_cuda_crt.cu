/*
 * ===========================================
 * 文件名: main_cuda_crt.cu
 * 描述: CUDA CRT（中国剩余定理）优化的NTT实现
 * 目标: 使用多个小模数并行计算，最后用CRT合并结果
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_crt.cu -o ntt_cuda_crt
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

// CRT模数（选择几个大的质数）
const int CRT_NUMS = 3;
const unsigned int CRT_MODS[CRT_NUMS] = {
    998244353,   // 119 * 2^23 + 1
    1004535809,  // 479 * 2^21 + 1  
    1012924417   // 483 * 2^21 + 1
};

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

inline long long qpow(long long x, long long y, unsigned int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// CUDA Kernel: 位反转置换
__global__ void bit_reverse_kernel(unsigned int *data, unsigned int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        unsigned int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

// CUDA Kernel: Radix-2 NTT蝶形运算
__global__ void ntt_kernel(unsigned int *data, int len, unsigned int wn, unsigned int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    // 计算旋转因子
    unsigned long long w = 1;
    for(int i = 0; i < local_id; i++) {
        w = (w * wn) % p;
    }
    
    unsigned int u = data[base + local_id];
    unsigned long long v = ((unsigned long long)data[base + local_id + half_len] * w) % p;
    
    data[base + local_id] = (u + v) % p;
    data[base + local_id + half_len] = (u + p - v) % p;
}

// CUDA Kernel: 数组缩放
__global__ void scale_kernel(unsigned int *data, unsigned int inv_n, int n, unsigned int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = ((unsigned long long)data[idx] * inv_n) % p;
    }
}

// CUDA Kernel: 点乘
__global__ void pointwise_mul_kernel(unsigned int *a, const unsigned int *b, int n, unsigned int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        a[idx] = ((unsigned long long)a[idx] * b[idx]) % p;
    }
}

// 单个模数的CUDA NTT
void cuda_ntt_single_mod(unsigned int *h_data, int n, bool inverse, unsigned int p) {
    unsigned int *d_data, *d_rev;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(unsigned int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(unsigned int)));

    // 准备位反转表
    std::vector<unsigned int> rev(n);
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }
    
    for(int i = 0; i < n; i++) {
        rev[i] = 0;
        for(int j = 0; j < lg; j++) {
            if(i & (1 << j)) {
                rev[i] |= 1 << (lg - 1 - j);
            }
        }
    }
    
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(unsigned int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(unsigned int), cudaMemcpyHostToDevice));
    
    // 位反转置换
    int threads = min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // NTT蝶形运算阶段
    for(int len = 2; len <= n; len <<= 1) {
        unsigned int wn = qpow(3, (p-1)/len, p);
        if(inverse) wn = qpow(wn, p-2, p);
        
        int half_len = len >> 1;
        int total_butterflies = n / len * half_len;
        threads = min(1024, total_butterflies);
        blocks = (total_butterflies + threads - 1) / threads;
        
        ntt_kernel<<<blocks, threads>>>(d_data, len, wn, p, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    // 逆变换的最终缩放
    if(inverse) {
        unsigned int inv_n = qpow(n, p-2, p);
        threads = min(1024, n);
        blocks = (n + threads - 1) / threads;
        scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(unsigned int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
}

// CUDA Kernel: CRT合并
__global__ void crt_combine_kernel(unsigned long long *result, 
                                  const unsigned int *data0, const unsigned int *data1, const unsigned int *data2,
                                  unsigned long long m0, unsigned long long m1, unsigned long long m2,
                                  unsigned long long inv01, unsigned long long inv02, unsigned long long inv12,
                                  int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx >= n) return;
    
    // 使用CRT公式合并三个模数的结果
    unsigned long long x0 = data0[idx];
    unsigned long long x1 = data1[idx];
    unsigned long long x2 = data2[idx];
    
    // 第一步：合并前两个模数
    unsigned long long diff1 = (x1 + m1 - x0 % m1) % m1;
    unsigned long long temp = x0 + m0 * ((diff1 * inv01) % m1);
    
    // 第二步：合并第三个模数
    unsigned long long m01 = m0 * m1;
    unsigned long long diff2 = (x2 + m2 - temp % m2) % m2;
    result[idx] = temp + m01 * ((diff2 * inv02) % m2);
}

// 计算模逆元
unsigned long long mod_inverse(unsigned long long a, unsigned long long m) {
    return qpow(a, m-2, m);
}

// 修改后的CRT多项式乘法
void cuda_poly_multiply_crt(int *a, int *b, int *result, int n, int p) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;

    // 预计算所有CRT参数（新增）
    unsigned long long m0 = CRT_MODS[0];
    unsigned long long m1 = CRT_MODS[1];
    unsigned long long m2 = CRT_MODS[2];
    unsigned long long inv01 = qpow(m0 % m1, m1-2, m1);
    unsigned long long inv02 = qpow(m0*m1 % m2, m2-2, m2);
    unsigned long long m01 = m0 * m1;

    // 分配GPU内存（新增）
    unsigned int *d_A_mods[CRT_NUMS], *d_B_mods[CRT_NUMS];
    unsigned long long *d_result;
    for(int i=0; i<CRT_NUMS; i++){
        CHECK_CUDA(cudaMalloc(&d_A_mods[i], lim*sizeof(unsigned int)));
        CHECK_CUDA(cudaMalloc(&d_B_mods[i], lim*sizeof(unsigned int)));
    }
    CHECK_CUDA(cudaMalloc(&d_result, (2*n-1)*sizeof(unsigned long long)));

    // 并行处理每个模数（优化为GPU流水线）
    for(int mod_idx = 0; mod_idx < CRT_NUMS; mod_idx++) {
        // 数据初始化移到GPU（优化点）
        unsigned int mod = CRT_MODS[mod_idx];
        CHECK_CUDA(cudaMemset(d_A_mods[mod_idx], 0, lim*sizeof(unsigned int)));
        CHECK_CUDA(cudaMemset(d_B_mods[mod_idx], 0, lim*sizeof(unsigned int)));
        
        // 使用CUDA内核初始化数据（新增内核）
        dim3 block(256);
        dim3 grid((n + block.x - 1)/block.x);
        initialize_data_kernel<<<grid, block>>>(d_A_mods[mod_idx], d_B_mods[mod_idx], 
                                             a, b, n, mod);
        
        // 变换和点乘保持原逻辑
        cuda_ntt_single_mod(d_A_mods[mod_idx], lim, false, mod);
        cuda_ntt_single_mod(d_B_mods[mod_idx], lim, false, mod);
        
        dim3 mul_grid((lim + block.x - 1)/block.x);
        pointwise_mul_kernel<<<mul_grid, block>>>(d_A_mods[mod_idx], d_B_mods[mod_idx], 
                                                 lim, mod);
        
        cuda_ntt_single_mod(d_A_mods[mod_idx], lim, true, mod);
    }

    // 使用GPU并行合并（新增内核调用）
    dim3 crt_block(256);
    dim3 crt_grid((2*n-1 + crt_block.x - 1)/crt_block.x);
    crt_combine_kernel<<<crt_grid, crt_block>>>(d_result, 
        d_A_mods[0], d_A_mods[1], d_A_mods[2],
        m0, m1, m2, inv01, inv02, m01, 2*n-1);

    // 拷贝结果回CPU（新增）
    unsigned long long *h_result = new unsigned long long[2*n-1];
    CHECK_CUDA(cudaMemcpy(h_result, d_result, (2*n-1)*sizeof(unsigned long long), 
                        cudaMemcpyDeviceToHost));

    // 最终模约简（优化处理负数）
    for(int i=0; i<2*n-1; i++) {
        result[i] = (h_result[i] % p + p) % p;  // 确保结果非负
    }

    // 释放内存（新增）
    for(int i=0; i<CRT_NUMS; i++){
        CHECK_CUDA(cudaFree(d_A_mods[i]));
        CHECK_CUDA(cudaFree(d_B_mods[i]));
    }
    CHECK_CUDA(cudaFree(d_result));
    delete[] h_result;
}

// 新增数据初始化内核（需要添加到文件头部）
__global__ void initialize_data_kernel(unsigned int *d_a, unsigned int *d_b, 
                                      const int *h_a, const int *h_b, int n, 
                                      unsigned int mod) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        d_a[idx] = ((long long)h_a[idx] % mod + mod) % mod;
        d_b[idx] = ((long long)h_b[idx] % mod + mod) % mod;
    }
}

int main() {
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("使用GPU: %s\n", prop.name);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);
    
    printf("CRT模数: ");
    for(int i = 0; i < CRT_NUMS; i++) {
        printf("%u ", CRT_MODS[i]);
    }
    printf("\n");
    
    int a[300000], b[300000], ab[300000];
    
    printf("\nCUDA CRT优化 NTT 实现测试:\n");
    printf("================================================================\n");
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_crt(a, b, ab, n, p);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
}
